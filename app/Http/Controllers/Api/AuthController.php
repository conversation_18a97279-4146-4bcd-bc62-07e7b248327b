<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
class AuthController extends Controller
{
    ///Unsafe dev only purposes thing.
    public function devLogin(Request $request)
    {
        $email = $request->input('email');

        $user = \App\Models\User::where('email', $email)->first();
       
        if (!$user) {
            $user = $this->createUser($email);
        }
        if ($user != null) {
            auth()->login($user);
            $sessionId = $user->createToken('api_token')->plainTextToken;
        
            return response()->json([
                'message' => 'Logged in as user',
                'session' => $sessionId
            ]);
        } else {
            return response()->json(['error' => 'Unable to login or create user'], 500);
        }
       
    }

    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();
        return response()->json(['message' => 'Logged out']);
    }

    public function user(Request $request)
    {
        return response()->json($request->user());
    }
    
    private function createUser($email)
    {
        return \App\Models\User::create([
            'email' => $email,
            'cookie-session' => '', // default password
        ]);
    }
}
