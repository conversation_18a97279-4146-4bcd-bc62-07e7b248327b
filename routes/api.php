<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\PostsController;


// USER
Route::post('/dev-login', [AuthController::class, 'devLogin']);
Route::middleware('auth:sanctum')->group(function () {
    // Secured
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);

    //POSTS
    Route::post('/post', [PostsController::class, 'store']);
    Route::get('/post', [PostsController::class, 'index']);
});

